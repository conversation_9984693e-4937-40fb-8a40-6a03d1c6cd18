# Skoon - Flutter Islamic App

<div style="display: flex; justify-content: space-around;">
  <img src="assets/screenshot2.jpg" alt="Screenshot 2" width="300">
  <img src="assets/screenshot1.jpg" alt="Screenshot 1" width="300">
  <img src="assets/screenshot3.jpg" alt="Screenshot 3" width="300">
  <img src="assets/demo.gif" alt="demo" width="300">
</div>

## Overview

Skoon is a comprehensive Islamic app built with Flutter. It offers a variety of features including Quran, Hadith, Quran audio, radio, the 99 names of <PERSON>, <PERSON><PERSON>, and more.
## Demo Apk

[Download Demo APK](https://drive.google.com/drive/folders/1WsFCFsHObbF4--v2sfog8ig8yzigSSoh?usp=sharing)  <!-- Replace # with your actual APK link -->

![Download APK](https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT3IsrHtOMd1RrrBrsKzF2EHLGydrhj6j4QVg&s)  <!-- Replace # with the actual image link of the download APK photo -->


## Features

- **Quran**: Complete Quran text with translations and tafsir.
- **Hadith**: Access to a collection of Hadith from various sources.
- **Quran Audio**: Listen to Quran recitations by different Qaris.
- **Radio**: Live streaming of Islamic radio stations.
- **99 Names of Allah**: Learn and reflect on the 99 beautiful names of Allah.
- **Duaa**: Collection of daily and special occasion supplications.
- **And more**: Additional features to enhance your Islamic knowledge and practice.

## Getting Started

To get started with the Skoon app, follow these steps:

1. **Clone the repository**
   ```sh
   git clone https://github.com/Epic-Apps-Hub/Skoon-Flutter-Islamic-App
2. **Use The Right Version**
   Make sure to begin using flutter version 3.19.5, will be updated perdiocally
