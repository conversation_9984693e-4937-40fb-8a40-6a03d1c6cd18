// package com.example.nabd;

// import io.flutter.app.FlutterApplication;
// import io.flutter.plugin.common.PluginRegistry;
// import io.flutter.plugin.common.PluginRegistry.PluginRegistrantCallback;
// import io.flutter.plugins.GeneratedPluginRegistrant;
// import io.flutter.plugins.androidalarmmanager.AlarmService;

// public class Application extends FlutterApplication implements PluginRegistrantCallback {
//   @Override
//   public void onCreate() {
//     super.onCreate();
//     AlarmService.setPluginRegistrant(this);
//   }

//   @Override
//   public void registerWith(PluginRegistry registry) {
//     GeneratedPluginRegistrant.registerWith(registry);
//   }
// }