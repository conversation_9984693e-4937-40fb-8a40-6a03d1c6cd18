buildscript {
    ext.kotlin_version = '1.7.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
                classpath 'com.google.gms:google-services:4.3.13'

    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
         configurations.all{
      resolutionStrategy{
         force "com.google.android.gms:play-services-location:21.0.1"
      }
 }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}
ext.flutterFFmpegPackage = 'full'

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
