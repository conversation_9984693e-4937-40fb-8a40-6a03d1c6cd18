List<Map<String, String>> books = [
  {"name": "<PERSON><PERSON> Daud", "arabicName": "أبي داود", "file": "abi_daud.json"},
  {"name": "<PERSON>", "arabicName": "أحمد", "file": "ahmed.json"},
  {"name": "<PERSON>uk<PERSON>", "arabicName": "البخاري", "file": "bukhari.json"},
  {"name": "<PERSON><PERSON>i", "arabicName": "الدارمي", "file": "darimi.json"},
  {"name": "<PERSON>", "arabicName": "ابن ماجه", "file": "ibn_maja.json"},
  {"name": "<PERSON>", "arabicName": "مالك", "file": "malik.json"},
  {"name": "Muslim", "arabicName": "مسلم", "file": "muslim.json"},
  {"name": "<PERSON><PERSON>", "arabicName": "النسائي", "file": "nasai.json"},
  {"name": "Trmizi", "arabicName": "الترمذي", "file": "trmizi.json"}
];

const baseHadithUrl="https://raw.githubusercontent.com/noureddin/Quran-App-Data/main/Hadith%20Books%20Json/";


class HadithModel {
  final int number;
  final String hadith;
  final String description;
  final String searchTerm;

  HadithModel({
    required this.number,
    required this.hadith,
    required this.description,
    required this.searchTerm,
  });

  factory HadithModel.fromJson(Map<String, dynamic> json) {
    return HadithModel(
      number: json['number'] as int,
      hadith: json['hadith'] as String,
      description: json['description'] as String,
      searchTerm: json['searchTerm'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'hadith': hadith,
      'description': description,
      'searchTerm': searchTerm,
    };
  }
}
